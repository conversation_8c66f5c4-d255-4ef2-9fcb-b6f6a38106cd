import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class AnalyticsEventsDataSessionDTO {
    @ApiProperty({
        description: 'Session ID',
        example: 'FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF',
        required: false
    })
    @IsString()
    @IsOptional()
    id?: string;

    @ApiProperty({
        description: 'User ID',
        example: '12345',
        required: false
    })
    @IsOptional()
    userId?: number | string;

    @ApiProperty({
        description: 'User object containing additional user data',
        required: false
    })
    @IsObject()
    @IsOptional()
    user?: object;

    [prop: string]: any;
}

export class AnalyticsEventDTO {
    @ApiProperty({
        description: 'Name of the event',
        example: 'user_login'
    })
    @IsString()
    name: string;

    @ApiProperty({
        description: 'Event data object',
        example: { loginMethod: 'email' }
    })
    @IsObject()
    data: object;
}

export class AnalyticsEventsDataDTO {
    @ApiProperty({
        description: 'Timestamp of the events',
        example: 1647123456789,
        required: false
    })
    @IsNumber()
    @IsOptional()
    time?: number;

    @ApiProperty({
        description: 'Geographic location of the events',
        example: 'US',
        required: false
    })
    @IsString()
    @IsOptional()
    geo?: string;

    @ApiProperty({
        description: 'Session information',
        type: AnalyticsEventsDataSessionDTO,
        required: false
    })
    @ValidateNested()
    @Type(() => AnalyticsEventsDataSessionDTO)
    @IsOptional()
    session?: AnalyticsEventsDataSessionDTO;

    @ApiProperty({
        description: 'Array of events',
        type: [AnalyticsEventDTO]
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => AnalyticsEventDTO)
    events: AnalyticsEventDTO[];
} 