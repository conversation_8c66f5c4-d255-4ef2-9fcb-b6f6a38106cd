import { Module } from '@nestjs/common';
import { NestJsClient, TNestJsHttpClientConfig } from '@superawesome/freekws-clients-nestjs';
import { MetricsService } from '@superawesome/freekws-metrics-nestjs-service';
import {
  SETTINGS_BACKEND_API_CLIENT_INJECT_KEY,
  SETTINGS_BACKEND_API_CLIENT_UPSTREAM,
  settingsBackendPlugin,
} from '@superawesome/freekws-settings-common';
import { AxiosError } from 'axios';
import CircuitBreaker from 'opossum';

import { SettingsService } from './settings.service';
import { CommonModule } from '../../common.module';
import { ConfigService } from '../config/config.service';
import { KeycloakModule } from '../keycloak/keycloak.module';

@Module({
  imports: [CommonModule, KeycloakModule],
  providers: [
    {
      provide: SETTINGS_BACKEND_API_CLIENT_INJECT_KEY,
      useFactory: (config: ConfigService, { metrics }: MetricsService) => {
        const { baseURL, ...clientOptions } = config.getSettingsService();
        const circuitBreakerConfig = config.getSettingsServiceCircuitBreakerConfig();
        const clientConfig: TNestJsHttpClientConfig = {
          timeout: clientOptions.timeoutMs,
          upstream: SETTINGS_BACKEND_API_CLIENT_UPSTREAM,
          retry: {
            retries: clientOptions.retries,
            initialRetryDelay: clientOptions.initialRetryDelay,
            bailOnStatus: clientOptions.bailOnStatus,
          },
          circuitBreaker: {
            timeout: circuitBreakerConfig.timeoutMs,
            errorThresholdPercentage: circuitBreakerConfig.errorThresholdPercentage,
            resetTimeout: circuitBreakerConfig.resetTimeoutMs,
            errorFilter: (error: AxiosError) => {
              return error.response?.status && error.response.status >= 400 && error.response.status < 500;
            },
          } as CircuitBreaker.Options,
        };
        return new NestJsClient(settingsBackendPlugin, baseURL, metrics, clientConfig);
      },
      inject: [ConfigService, MetricsService],
    },
    SettingsService,
  ],
  exports: [SettingsService],
})
export class SettingsModule {}
